#!/bin/bash

# Script para identificar el modelo y marca del router
ROUTER_IP="************"

echo "=== Identificación del Router ==="
echo "IP: $ROUTER_IP"
echo ""

echo "🔍 Obteniendo información del router..."

# Intentar obtener información del servidor
echo "Información del servidor web:"
curl -I -s -k https://$ROUTER_IP 2>/dev/null | grep -i "server\|x-powered\|x-router"

echo ""
echo "🌐 URLs detectadas del router:"

# Seguir redirecciones para encontrar páginas reales
echo "1. Página principal:"
curl -s -k https://$ROUTER_IP 2>/dev/null | grep -o 'href="[^"]*"' | head -5

echo ""
echo "2. Página de login:"
curl -s -k https://$ROUTER_IP/login.html 2>/dev/null | grep -o 'href="[^"]*"' | head -5

echo ""
echo "🔧 Intentando identificar marca/modelo..."

# Buscar en diferentes páginas comunes
PAGES=("/" "/login.html" "/status_deviceinfo.html" "/info.html" "/status.html")

for page in "${PAGES[@]}"; do
    echo "Verificando: https://$ROUTER_IP$page"
    CONTENT=$(curl -s -k -m 5 https://$ROUTER_IP$page 2>/dev/null)
    
    # Buscar marcas comunes
    if echo "$CONTENT" | grep -qi "tp-link\|tplink"; then
        echo "✅ Posible router TP-Link detectado"
    elif echo "$CONTENT" | grep -qi "netgear"; then
        echo "✅ Posible router Netgear detectado"
    elif echo "$CONTENT" | grep -qi "linksys"; then
        echo "✅ Posible router Linksys detectado"
    elif echo "$CONTENT" | grep -qi "asus"; then
        echo "✅ Posible router ASUS detectado"
    elif echo "$CONTENT" | grep -qi "d-link\|dlink"; then
        echo "✅ Posible router D-Link detectado"
    elif echo "$CONTENT" | grep -qi "huawei"; then
        echo "✅ Posible router Huawei detectado"
    elif echo "$CONTENT" | grep -qi "zte"; then
        echo "✅ Posible router ZTE detectado"
    fi
    
    # Buscar título de la página
    TITLE=$(echo "$CONTENT" | grep -i "<title>" | sed 's/<[^>]*>//g' | xargs)
    if [ ! -z "$TITLE" ]; then
        echo "Título de la página: $TITLE"
    fi
done

echo ""
echo "📋 Información de red adicional:"
echo "MAC del router: $(arp -n $ROUTER_IP 2>/dev/null | awk '{print $3}' | grep -v incomplete)"

echo ""
echo "🔑 Credenciales por defecto según marca:"
echo "TP-Link:  admin/admin"
echo "Netgear:  admin/password"
echo "Linksys:  admin/(vacío) o admin/admin"
echo "ASUS:     admin/admin"
echo "D-Link:   admin/(vacío)"
echo "Huawei:   admin/admin"
echo "ZTE:      admin/admin"

echo ""
echo "🌐 Páginas para probar en el navegador:"
echo "https://************/login.html"
echo "https://************/status_deviceinfo.html"
echo "https://************"
