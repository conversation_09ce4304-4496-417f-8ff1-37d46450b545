#!/bin/bash

# Script para despertar el equipo desde internet
# Configuración
PUBLIC_IP="*************"
MAC_ADDRESS="fc:34:97:e1:9a:9b"
WOL_PORT="9"

echo "=== Wake-On-LAN Remoto ==="
echo "IP Pública: $PUBLIC_IP"
echo "MAC Address: $MAC_ADDRESS"
echo "Puerto WOL: $WOL_PORT"
echo ""

echo "Enviando magic packet remoto..."
wakeonlan -i $PUBLIC_IP -p $WOL_PORT $MAC_ADDRESS

if [ $? -eq 0 ]; then
    echo "✅ Magic packet enviado exitosamente!"
    echo ""
    echo "El equipo debería encenderse en 10-30 segundos"
    echo "si la configuración del router es correcta."
else
    echo "❌ Error enviando magic packet"
    echo ""
    echo "Verifica:"
    echo "1. Configuración del router (port forwarding)"
    echo "2. Conexión a internet"
    echo "3. Que el equipo esté conectado por cable"
fi

echo ""
echo "Para usar desde otro dispositivo:"
echo "wakeonlan -i $PUBLIC_IP -p $WOL_PORT $MAC_ADDRESS"
