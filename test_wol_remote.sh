#!/bin/bash

# Script para probar WOL remoto sin necesidad de root
PUBLIC_IP="*************"
MAC_ADDRESS="fc:34:97:e1:9a:9b"
WOL_PORT="9"

echo "=== Prueba de Wake-On-LAN Remoto ==="
echo "IP Pública: $PUBLIC_IP"
echo "MAC Address: $MAC_ADDRESS"
echo "Puerto WOL: $WOL_PORT"
echo ""

echo "🔍 Verificando conectividad básica..."

# Ping a la IP pública
echo "Haciendo ping a $PUBLIC_IP..."
if ping -c 3 $PUBLIC_IP > /dev/null 2>&1; then
    echo "✅ Ping exitoso - IP pública accesible"
else
    echo "❌ Ping falló - Verifica conexión a internet"
fi

echo ""
echo "🚀 Enviando magic packet remoto..."

# Intentar enviar magic packet
if wakeonlan -i $PUBLIC_IP -p $WOL_PORT $MAC_ADDRESS; then
    echo "✅ Comando ejecutado exitosamente"
    echo ""
    echo "Si el router está configurado correctamente:"
    echo "- El equipo debería encenderse en 10-30 segundos"
    echo "- Verifica las luces del equipo"
    echo "- Intenta hacer ping en unos minutos"
else
    echo "❌ Error ejecutando comando"
fi

echo ""
echo "📋 Para configurar el router:"
echo "1. Ve a: http://************"
echo "2. Busca 'Port Forwarding' o 'Virtual Servers'"
echo "3. Agrega:"
echo "   - Protocolo: UDP"
echo "   - Puerto Externo: $WOL_PORT"
echo "   - IP Interna: **************"
echo "   - Puerto Interno: $WOL_PORT"

echo ""
echo "📱 Para apps móviles usa:"
echo "   - Host: $PUBLIC_IP"
echo "   - Puerto: $WOL_PORT"
echo "   - MAC: $MAC_ADDRESS"

echo ""
echo "🔧 Alternativas si no funciona:"
echo "1. Usar VPN para conectarte a tu red local"
echo "2. Configurar DynDNS con: ./setup_dyndns.sh"
echo "3. Cambiar puerto a 9999 para evitar filtros ISP"
