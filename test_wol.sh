#!/bin/bash

# Script para probar Wake-On-LAN localmente
MAC_ADDRESS="fc:34:97:e1:9a:9b"

echo "=== Prueba de Wake-On-LAN ==="
echo "Enviando magic packet a: $MAC_ADDRESS"
echo ""

# Enviar magic packet
wol $MAC_ADDRESS

echo "Magic packet enviado!"
echo ""
echo "Nota: Esta es solo una prueba local."
echo "Para una prueba real, necesitas:"
echo "1. <PERSON>pagar completamente el equipo"
echo "2. Enviar el magic packet desde otro dispositivo en la misma red"
echo "3. El equipo debería encenderse automáticamente"
