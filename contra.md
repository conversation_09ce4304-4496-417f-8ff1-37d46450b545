# 🔐 Credenciales RustDesk + Wake-On-LAN

## 📋 Información del Sistema Principal (Manjaro Plasma)
- **ID RustDesk**: 400661147
- **Contraseña**: WakeUp2025!
- **IP Local**: *************
- **MAC Address**: fc:34:97:e1:9a:9b

## 🖥️ <PERSON><PERSON><PERSON> (Manjaro XFCE)
- **ID RustDesk**: [Por configurar]
- **Contraseña**: [Por configurar - usar la misma: WakeUp2025!]

## 📱 Para Apps Móviles
### Conexión a Manjaro Plasma:
- **ID**: 400661147
- **Contraseña**: WakeUp2025!
- **Alias**: "Manjaro Plasma"

### Conexión a Manjaro XFCE:
- **ID**: [Configurar después]
- **Contraseña**: WakeUp2025!
- **Alias**: "Manjaro XFCE Bridge"

## 🚀 Flujo de Uso Remoto
1. Conectar a XFCE via RustDesk (ID: [Por configurar])
2. Ejecutar: `./despertar_plasma_rustdesk.sh`
3. Esperar 30-90 segundos
4. Conectar a Plasma via RustDesk (ID: 400661147)

## 🔧 Comandos Útiles
```bash
# Despertar Plasma desde XFCE:
wakeonlan fc:34:97:e1:9a:9b

# Verificar si Plasma está despierto:
ping *************

# Ejecutar RustDesk:
flatpak run com.rustdesk.RustDesk &
```

## ⚠️ Notas de Seguridad
- Contraseña generada automáticamente
- Cambiar si es necesario por una más personal
- No compartir estas credenciales
- Usar solo en redes confiables

---
**Fecha de creación**: $(date)
**Sistema**: Manjaro Plasma
**Configurado por**: Augment Agent
