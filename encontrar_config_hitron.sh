#!/bin/bash

# Script para encontrar páginas de configuración en router Hitron
ROUTER_IP="************"

echo "=== Buscando Páginas de Configuración - Hitron ==="
echo ""

# URLs comunes para configuración en routers Hitron
URLS=(
    "/login.html"
    "/index.html"
    "/main.html"
    "/home.html"
    "/config.html"
    "/advanced.html"
    "/firewall.html"
    "/portforward.html"
    "/port_forwarding.html"
    "/virtual_server.html"
    "/nat.html"
    "/network.html"
    "/admin.html"
    "/setup.html"
    "/configuration.html"
    "/settings.html"
    "/management.html"
    "/system.html"
)

echo "🔍 Probando URLs de configuración..."
echo ""

for url in "${URLS[@]}"; do
    echo -n "Probando: https://$ROUTER_IP$url ... "
    
    # Verificar si la URL responde
    HTTP_CODE=$(curl -s -k -o /dev/null -w "%{http_code}" https://$ROUTER_IP$url 2>/dev/null)
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ ENCONTRADA"
        echo "   → https://$ROUTER_IP$url"
    elif [ "$HTTP_CODE" = "302" ] || [ "$HTTP_CODE" = "301" ]; then
        echo "🔄 REDIRECCIÓN"
        # Seguir redirección
        REDIRECT=$(curl -s -k -I https://$ROUTER_IP$url 2>/dev/null | grep -i "location:" | cut -d' ' -f2 | tr -d '\r')
        if [ ! -z "$REDIRECT" ]; then
            echo "   → Redirige a: $REDIRECT"
        fi
    elif [ "$HTTP_CODE" = "401" ]; then
        echo "🔐 REQUIERE LOGIN"
        echo "   → https://$ROUTER_IP$url (necesita autenticación)"
    else
        echo "❌ No encontrada ($HTTP_CODE)"
    fi
done

echo ""
echo "🌐 URLs para probar en tu navegador:"
echo "1. https://************/login.html"
echo "2. https://************/index.html"
echo "3. https://************/main.html"
echo "4. https://************/advanced.html"
echo "5. https://************/firewall.html"

echo ""
echo "🔑 Si aparece login, usa:"
echo "Usuario: admin | Contraseña: admin"
echo "Usuario: admin | Contraseña: password"
echo "Usuario: cusadmin | Contraseña: password"

echo ""
echo "📋 Una vez dentro, busca:"
echo "- Port Forwarding"
echo "- Virtual Servers"
echo "- Firewall Settings"
echo "- Advanced → Network"
