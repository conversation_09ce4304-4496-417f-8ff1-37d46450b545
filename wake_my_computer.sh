#!/bin/bash

# Script para despertar este equipo usando Wake-On-LAN
# Dirección MAC de la interfaz eno1: fc:34:97:e1:9a:9b

MAC_ADDRESS="fc:34:97:e1:9a:9b"
INTERFACE="eno1"

echo "=== Configuración Wake-On-LAN ==="
echo "Interfaz de red: $INTERFACE"
echo "Dirección MAC: $MAC_ADDRESS"
echo ""

# Verificar estado actual de WOL
echo "Estado actual de Wake-On-LAN:"
sudo ethtool $INTERFACE | grep "Wake-on"
echo ""

# Para despertar este equipo desde otro dispositivo, usa:
echo "Para despertar este equipo desde otro dispositivo, ejecuta:"
echo "wol $MAC_ADDRESS"
echo ""
echo "O desde Windows:"
echo "wakeonlan $MAC_ADDRESS"
echo ""
echo "O usando una app móvil de Wake-On-LAN con la MAC: $MAC_ADDRESS"
