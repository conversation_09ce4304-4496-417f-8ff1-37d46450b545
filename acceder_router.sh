#!/bin/bash

# Script para ayudar a acceder al router
ROUTER_IP="************"

echo "=== Acceso al Router - Diagnóstico ==="
echo "IP del Router: $ROUTER_IP"
echo ""

echo "🔍 Verificando conectividad..."
if ping -c 1 $ROUTER_IP > /dev/null 2>&1; then
    echo "✅ Router responde al ping"
else
    echo "❌ Router no responde al ping"
    exit 1
fi

echo ""
echo "🌐 URLs para probar en tu navegador:"
echo "1. https://************/login.html (RECOMENDADO)"
echo "2. https://************"
echo "3. http://************"
echo "4. https://************:8443"
echo "5. http://************:8080"

echo ""
echo "🔑 Credenciales comunes para probar:"
echo "Usuario: admin    | Contraseña: admin"
echo "Usuario: admin    | Contraseña: password"
echo "Usuario: admin    | Contraseña: 1234"
echo "Usuario: admin    | Contraseña: (vacía)"
echo "Usuario: root     | Contraseña: admin"
echo "Usuario: (vacío)  | Contraseña: admin"

echo ""
echo "📋 Información del router detectada:"
echo "Servidor: GoAhead-Webs"
echo "Redirección automática a HTTPS habilitada"

echo ""
echo "🔧 Si no puedes acceder, intenta:"
echo "1. Reiniciar el router (desconectar 30 segundos)"
echo "2. Usar un navegador diferente"
echo "3. Limpiar caché del navegador"
echo "4. Deshabilitar extensiones del navegador"
echo "5. Probar desde modo incógnito"

echo ""
echo "🏷️ Para encontrar credenciales:"
echo "1. Revisar etiqueta en el router (usuario/contraseña)"
echo "2. Buscar modelo del router en Google"
echo "3. Revisar manual del router"

echo ""
echo "⚠️ Si nada funciona:"
echo "1. Reset de fábrica (botón reset 10 segundos)"
echo "2. Contactar al ISP si es router del proveedor"

echo ""
echo "🚀 Abriendo navegador automáticamente..."
