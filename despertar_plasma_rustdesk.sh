#!/bin/bash

# Script para despertar Manjaro Plasma desde XFCE via RustDesk
# Usar este script en el sistema XFCE

PLASMA_IP="*************"
PLASMA_MAC="fc:34:97:e1:9a:9b"
PLASMA_NAME="Manjaro Plasma"

echo "🚀 === Wake-On-LAN via RustDesk ==="
echo "Sistema objetivo: $PLASMA_NAME"
echo "IP: $PLASMA_IP"
echo "MAC: $PLASMA_MAC"
echo ""

# Verificar si ya está encendido
echo "🔍 Verificando si $PLASMA_NAME ya está encendido..."
if ping -c 2 $PLASMA_IP > /dev/null 2>&1; then
    echo "✅ $PLASMA_NAME ya está encendido y respondiendo"
    echo "🖥️ Puedes conectarte directamente via RustDesk"
    echo ""
    echo "ID RustDesk del Plasma: 400661147"
    exit 0
fi

echo "💤 $PLASMA_NAME está apagado. Despertando..."

# Verificar que wakeonlan esté instalado
if ! command -v wakeonlan &> /dev/null; then
    echo "❌ wakeonlan no está instalado"
    echo "Instalando wakeonlan..."
    sudo pacman -S wakeonlan
fi

# Enviar magic packet
echo "📡 Enviando magic packet a $PLASMA_MAC..."
wakeonlan $PLASMA_MAC

if [ $? -eq 0 ]; then
    echo "✅ Magic packet enviado exitosamente"
else
    echo "❌ Error enviando magic packet"
    exit 1
fi

echo ""
echo "⏳ Esperando que $PLASMA_NAME inicie..."
echo "Esto puede tomar 30-90 segundos dependiendo del hardware"
echo ""

# Contador con verificación
for i in {90..1}; do
    # Verificar cada 5 segundos si está respondiendo
    if [ $((i % 5)) -eq 0 ]; then
        if ping -c 1 $PLASMA_IP > /dev/null 2>&1; then
            TIEMPO_TRANSCURRIDO=$((91-i))
            echo ""
            echo "🎉 ¡$PLASMA_NAME está listo!"
            echo "⏱️ Tiempo de inicio: $TIEMPO_TRANSCURRIDO segundos"
            echo ""
            echo "🖥️ Ahora puedes conectarte via RustDesk:"
            echo "   1. Abrir RustDesk"
            echo "   2. Conectar a ID del Plasma: 400661147"
            echo "   3. Usar contraseña: WakeUp2025!"
            echo "   4. ¡Listo para usar!"
            echo ""
            
            # Opcional: Mostrar información de conexión
            echo "📋 Información de conexión:"
            echo "   IP Local: $PLASMA_IP"
            echo "   Estado: Activo y listo"
            
            exit 0
        fi
    fi
    
    # Mostrar progreso cada 10 segundos
    if [ $((i % 10)) -eq 0 ]; then
        echo "⏳ Esperando... $i segundos restantes"
    fi
    
    sleep 1
done

echo ""
echo "⚠️ $PLASMA_NAME no respondió en 90 segundos"
echo ""
echo "🔧 Posibles causas:"
echo "   1. El equipo tarda más en iniciar (normal en algunos casos)"
echo "   2. Wake-On-LAN no está configurado correctamente"
echo "   3. El cable Ethernet no está conectado"
echo "   4. El equipo está en suspensión profunda"
echo ""
echo "💡 Soluciones:"
echo "   1. Espera 1-2 minutos más y prueba conectar via RustDesk"
echo "   2. Verifica la configuración WOL: ./wake_my_computer.sh"
echo "   3. Intenta de nuevo: ./despertar_plasma_rustdesk.sh"
echo ""
echo "🔄 Para verificar manualmente:"
echo "   ping $PLASMA_IP"
