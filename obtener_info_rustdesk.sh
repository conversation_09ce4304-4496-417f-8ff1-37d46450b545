#!/bin/bash

# Script para obtener información de RustDesk y configurar Wake-On-LAN remoto

echo "🔍 === Información RustDesk + Wake-On-LAN ==="
echo ""

# Verificar si RustDesk está corriendo
if pgrep -x "rustdesk" > /dev/null; then
    echo "✅ RustDesk está ejecutándose"
else
    echo "⚠️ RustDesk no está ejecutándose"
    echo "   Por favor, abre RustDesk primero"
    exit 1
fi

echo ""
echo "📋 Información del sistema actual (Manjaro Plasma):"
echo "   IP Local: $(ip route get ******* | grep -oP 'src \K\S+')"
echo "   MAC Address: fc:34:97:e1:9a:9b"
echo "   Interfaz: eno1"
echo ""

echo "🔧 Para completar la configuración necesitas:"
echo ""
echo "1. 📝 **Anotar tu ID de RustDesk**:"
echo "   - Mira la ventana de RustDesk que tienes abierta"
echo "   - Anota el número ID que aparece"
echo "   - Ejemplo: 123456789"
echo ""

echo "2. 🔐 **Configurar contraseña fija en RustDesk**:"
echo "   - En RustDesk: Clic en ⚙️ (configuración)"
echo "   - Ir a 'Security' → 'Password'"
echo "   - Establecer contraseña permanente"
echo "   - ✅ Marcar 'Enable Password'"
echo ""

echo "3. 🖥️ **En tu Manjaro XFCE**:"
echo "   - Instalar RustDesk: sudo pacman -S rustdesk"
echo "   - Copiar este archivo: despertar_plasma_rustdesk.sh"
echo "   - Editar el script con tu ID real de RustDesk"
echo ""

echo "4. 📱 **En tus dispositivos móviles/remotos**:"
echo "   - Instalar app RustDesk"
echo "   - Configurar conexión a XFCE (sistema puente)"
echo "   - Configurar conexión a Plasma (este sistema)"
echo ""

echo "🚀 **Flujo de uso remoto**:"
echo "   1. Conectar a XFCE via RustDesk"
echo "   2. Ejecutar: ./despertar_plasma_rustdesk.sh"
echo "   3. Esperar 30-90 segundos"
echo "   4. Conectar a Plasma via RustDesk"
echo ""

echo "📋 **Información para apps móviles**:"
echo ""
echo "   🖥️ Manjaro Plasma (este sistema):"
echo "   - ID: [Tu ID de RustDesk - anótalo ahora]"
echo "   - Contraseña: [Tu contraseña configurada]"
echo "   - Propósito: Sistema principal"
echo ""
echo "   🖥️ Manjaro XFCE (sistema puente):"
echo "   - ID: [ID del XFCE - configurar después]"
echo "   - Contraseña: [Contraseña del XFCE]"
echo "   - Propósito: Para despertar Plasma"
echo ""

echo "🔧 **Scripts disponibles**:"
echo "   - despertar_plasma_rustdesk.sh (para usar en XFCE)"
echo "   - RUSTDESK_CONFIGURACION_COMPLETA.md (guía completa)"
echo "   - wake_my_computer.sh (verificar WOL local)"
echo ""

echo "✅ **Ventajas de esta solución**:"
echo "   ✅ No necesitas configurar el router"
echo "   ✅ Funciona desde cualquier lugar del mundo"
echo "   ✅ Conexión segura y encriptada"
echo "   ✅ Gratis y open source"
echo "   ✅ Apps disponibles para todos los dispositivos"
echo ""

echo "🎯 **Próximo paso**: Anota tu ID de RustDesk y configura el XFCE"

# Crear archivo de configuración para recordar
cat > rustdesk_config.txt << EOF
# Configuración RustDesk + Wake-On-LAN
# Fecha: $(date)

## Manjaro Plasma (sistema principal)
ID_PLASMA=[Anotar aquí tu ID de RustDesk]
PASSWORD_PLASMA=[Tu contraseña configurada]
IP_PLASMA=*************
MAC_PLASMA=fc:34:97:e1:9a:9b

## Manjaro XFCE (sistema puente)
ID_XFCE=[Configurar después]
PASSWORD_XFCE=[Configurar después]

## Comandos útiles
# Despertar Plasma desde XFCE:
# ./despertar_plasma_rustdesk.sh

# Verificar WOL local:
# ./wake_my_computer.sh

# Verificar si Plasma está despierto:
# ping *************
EOF

echo ""
echo "📄 Archivo de configuración creado: rustdesk_config.txt"
echo "   Edita este archivo con tus IDs y contraseñas reales"
