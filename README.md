# 🌐 Wake-On-LAN Completo - Manjaro Plasma

## 📋 Información de Tu Sistema
- **Interfaz de red**: eno1
- **MAC Address**: fc:34:97:e1:9a:9b
- **IP Local**: *************
- **IP Pública**: *************
- **Gateway**: ************

## ✅ Estado Actual
- ✅ Wake-On-LAN configurado y activo
- ✅ Servicio systemd habilitado (persiste después de reinicios)
- ✅ Scripts de prueba y configuración creados

## 📁 Archivos Disponibles

### 📖 Documentación
- `README.md` - Este archivo (resumen general)
- `WOL_CONFIGURACION.md` - Configuración local completa
- `WOL_REMOTO_GUIA.md` - Guía completa para WOL remoto

### 🔧 Scripts Locales
- `wake_my_computer.sh` - Muestra información de configuración
- `test_wol.sh` - Prueba WOL localmente

### 🌐 Scripts Remotos
- `wake_remote.sh` - Despertar desde internet
- `test_wol_remote.sh` - <PERSON>bar W<PERSON> remoto (recomendado)
- `check_wol_port.sh` - Verificar configuración del router
- `setup_dyndns.sh` - Configurar dominio dinámico

## 🚀 Uso Rápido

### WOL Local (misma red):
```bash
./test_wol.sh
```

### WOL Remoto (desde internet):
```bash
./test_wol_remote.sh
```

### Verificar configuración:
```bash
./wake_my_computer.sh
```

## ⚙️ Configuración del Router (OBLIGATORIO para WOL remoto)

1. **Acceder al router**: http://************
2. **Buscar**: "Port Forwarding" o "Virtual Servers"
3. **Agregar regla**:
   - Protocolo: UDP
   - Puerto Externo: 9
   - IP Interna: **************
   - Puerto Interno: 9
   - Estado: Habilitado

## 📱 Apps Móviles

### Configuración para apps:
- **Host/IP**: *************
- **Puerto**: 9
- **MAC**: fc:34:97:e1:9a:9b
- **Protocolo**: UDP

### Apps recomendadas:
- **Android**: "Wake On Lan" (Mike Webb)
- **iOS**: "Mocha WOL"

## 🔒 Alternativa Segura: VPN

Para mayor seguridad, considera usar VPN en lugar de abrir puertos:
1. Configurar VPN en tu router (WireGuard/OpenVPN)
2. Conectarte por VPN desde cualquier lugar
3. Usar WOL local: `wol fc:34:97:e1:9a:9b`

## 🌍 IP Dinámica

Si tu IP pública cambia frecuentemente:
```bash
./setup_dyndns.sh
```

## 🧪 Pruebas Paso a Paso

### 1. Verificar WOL local:
```bash
./wake_my_computer.sh
```

### 2. Probar WOL remoto:
```bash
./test_wol_remote.sh
```

### 3. Si no funciona, verificar router:
```bash
./check_wol_port.sh
```

## ⚠️ Configuración BIOS/UEFI

**IMPORTANTE**: Verifica en tu BIOS/UEFI que esté habilitado:
- Wake on LAN / WOL
- Power on by PCI-E / Ethernet
- Resume by LAN

Y deshabilitado:
- Deep Sleep
- ErP Ready

## 🚨 Solución de Problemas

### WOL Local no funciona:
1. Verificar que el cable Ethernet esté conectado
2. Reiniciar servicio: `sudo systemctl restart wol.service`
3. Verificar BIOS/UEFI

### WOL Remoto no funciona:
1. Verificar configuración del router
2. Probar con puerto diferente (9999)
3. Verificar que ISP no bloquee puertos
4. Considerar usar VPN

## 📞 Comandos Útiles

### Verificar estado WOL:
```bash
sudo ethtool eno1 | grep Wake-on
```

### Reconfigurar WOL:
```bash
sudo ethtool -s eno1 wol g
```

### Ver IP pública actual:
```bash
curl ifconfig.me
```

### Despertar desde línea de comandos:
```bash
# Local:
wol fc:34:97:e1:9a:9b

# Remoto:
wakeonlan -i ************* -p 9 fc:34:97:e1:9a:9b
```

## 🎯 Próximos Pasos

1. **Configurar router** (obligatorio para WOL remoto)
2. **Probar WOL local** con `./test_wol.sh`
3. **Probar WOL remoto** con `./test_wol_remote.sh`
4. **Configurar apps móviles** con los datos proporcionados
5. **Opcional**: Configurar DynDNS con `./setup_dyndns.sh`

---

**¡Wake-On-LAN está listo para usar!** 🎉

Para soporte adicional, revisa los archivos de documentación detallada.
