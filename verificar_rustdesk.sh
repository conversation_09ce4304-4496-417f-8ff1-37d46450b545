#!/bin/bash

# Script para verificar configuración de RustDesk y WOL
echo "🔍 === Verificación RustDesk + Wake-On-LAN ==="
echo ""

# Verificar RustDesk
echo "📱 Verificando RustDesk..."
if command -v rustdesk &> /dev/null; then
    echo "✅ RustDesk está instalado"
    
    # Verificar si está corriendo
    if pgrep -x "rustdesk" > /dev/null; then
        echo "✅ RustDesk está ejecutándose"
    else
        echo "⚠️ RustDesk no está ejecutándose"
        echo "   Iniciando RustDesk..."
        rustdesk &
        sleep 2
    fi
else
    echo "❌ RustDesk no está instalado"
    echo "   Instalar con: sudo pacman -S rustdesk"
fi

echo ""

# Verificar Wake-On-LAN
echo "🌐 Verificando Wake-On-LAN..."
if command -v wakeonlan &> /dev/null; then
    echo "✅ wakeonlan está instalado"
else
    echo "❌ wakeonlan no está instalado"
    echo "   Instalando wakeonlan..."
    sudo pacman -S wakeonlan
fi

if command -v wol &> /dev/null; then
    echo "✅ wol está instalado"
else
    echo "⚠️ wol no está instalado (opcional)"
fi

echo ""

# Verificar configuración WOL local
echo "⚙️ Verificando configuración WOL local..."
if [ -f "./wake_my_computer.sh" ]; then
    echo "✅ Script de configuración WOL encontrado"
    echo "   Ejecutando verificación..."
    ./wake_my_computer.sh
else
    echo "⚠️ Script de configuración WOL no encontrado"
    echo "   Verifica manualmente: sudo ethtool eno1 | grep Wake-on"
fi

echo ""

# Información de red
echo "🌐 Información de red actual..."
echo "IP local: $(ip route get ******* | grep -oP 'src \K\S+')"
echo "Gateway: $(ip route | grep default | awk '{print $3}')"

echo ""

# Verificar conectividad local
echo "🔗 Verificando conectividad en red local..."
NETWORK="192.168.10"
echo "Escaneando red $NETWORK.0/24..."

# Buscar otros dispositivos en la red
for i in {1..20}; do
    if ping -c 1 -W 1 $NETWORK.$i > /dev/null 2>&1; then
        echo "✅ Dispositivo encontrado: $NETWORK.$i"
    fi
done

echo ""

# Información para configurar RustDesk
echo "📋 Configuración recomendada para RustDesk:"
echo ""
echo "1. 🖥️ En Manjaro Plasma (sistema a despertar):"
echo "   - Instalar RustDesk: sudo pacman -S rustdesk"
echo "   - Configurar contraseña fija en RustDesk"
echo "   - Anotar ID de RustDesk"
echo "   - Configurar inicio automático"
echo ""
echo "2. 🖥️ En Manjaro XFCE (sistema puente):"
echo "   - Instalar RustDesk: sudo pacman -S rustdesk"
echo "   - Copiar script: despertar_plasma_rustdesk.sh"
echo "   - Crear acceso directo en escritorio"
echo ""
echo "3. 📱 En dispositivos remotos:"
echo "   - Instalar app RustDesk"
echo "   - Configurar conexiones a ambos sistemas"
echo "   - Probar conexión local primero"
echo ""

# Instrucciones de uso
echo "🚀 Instrucciones de uso remoto:"
echo ""
echo "1. Conectar a Manjaro XFCE via RustDesk"
echo "2. Ejecutar: ./despertar_plasma_rustdesk.sh"
echo "3. Esperar 30-90 segundos"
echo "4. Conectar a Manjaro Plasma via RustDesk"
echo ""

echo "✅ Verificación completada"
echo ""
echo "📝 Próximos pasos:"
echo "1. Configurar RustDesk en ambos sistemas"
echo "2. Probar despertar_plasma_rustdesk.sh localmente"
echo "3. Probar acceso remoto via RustDesk"
