# 🎯 RustDesk + Wake-On-LAN - Configuración Completa

## ✅ Estado Actual
- ✅ RustDesk instalado y funcionando
- ✅ Wake-On-LAN configurado en Manjaro Plasma
- ✅ Scripts WOL creados y listos
- 🎯 **Objetivo**: Despertar Plasma remotamente via RustDesk

## 🔧 Configuración Paso a Paso

### **1. En tu Manjaro Plasma (sistema actual)**

#### Obtener ID de RustDesk:
1. **Abrir RustDesk** (ya lo tienes abierto)
2. **Anotar tu ID** (número que aparece en la ventana principal)
3. **Configurar contraseña fija**:
   - Clic en ⚙️ (configuración)
   - "Security" → "Password"
   - Establecer contraseña permanente
   - ✅ "Enable Password"

#### Configurar inicio automático:
```bash
# Agregar RustDesk al inicio automático
mkdir -p ~/.config/autostart
cat > ~/.config/autostart/rustdesk.desktop << 'EOF'
[Desktop Entry]
Type=Application
Name=RustDesk
Exec=rustdesk --service
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
EOF
```

### **2. En tu Manjaro XFCE (sistema puente)**

#### Instalar RustDesk si no está:
```bash
sudo pacman -S rustdesk
```

#### Copiar scripts WOL:
```bash
# Copiar el script de despertar
scp despertar_plasma_rustdesk.sh usuario@ip-xfce:~/
# O usar USB/red compartida para transferir
```

#### Configurar script en XFCE:
```bash
# En el sistema XFCE, editar el script con tu ID real
nano ~/despertar_plasma_rustdesk.sh

# Cambiar esta línea:
# echo "ID RustDesk del Plasma: [Anota aquí tu ID]"
# Por:
# echo "ID RustDesk del Plasma: TU_ID_REAL_AQUI"
```

### **3. Crear Acceso Directo en XFCE**

#### Método 1: Lanzador en escritorio
1. **Clic derecho en escritorio** → "Create Launcher"
2. **Name**: "Despertar Manjaro Plasma"
3. **Command**: `/home/<USER>/despertar_plasma_rustdesk.sh`
4. **Icon**: Elegir icono reconocible
5. **✅ Executable**: Marcar casilla

#### Método 2: Acceso rápido en terminal
```bash
# Crear alias para acceso rápido
echo 'alias despertar="~/despertar_plasma_rustdesk.sh"' >> ~/.bashrc
source ~/.bashrc
```

## 🚀 Uso Remoto - Flujo Completo

### **Desde cualquier lugar del mundo:**

1. **📱 Abrir RustDesk** en tu dispositivo remoto (móvil/PC)
2. **🔗 Conectar a Manjaro XFCE** (que debe estar siempre encendido)
3. **⚡ Ejecutar despertar**:
   - Doble clic en "Despertar Manjaro Plasma" (escritorio)
   - O en terminal: `./despertar_plasma_rustdesk.sh`
4. **⏳ Esperar 30-90 segundos**
5. **🖥️ Conectar a Manjaro Plasma** con tu ID de RustDesk

### **Apps RustDesk recomendadas:**
- **Android**: RustDesk (Google Play)
- **iOS**: RustDesk (App Store)
- **Windows**: RustDesk Desktop
- **macOS**: RustDesk para Mac

## 📋 Información para Configurar Apps

### **Manjaro XFCE (Puente)**:
- **ID RustDesk**: [Anotar cuando configures XFCE]
- **Contraseña**: [Tu contraseña elegida]
- **Uso**: Para ejecutar comando despertar

### **Manjaro Plasma (Objetivo)**:
- **ID RustDesk**: [Tu ID actual que ya tienes]
- **Contraseña**: [Tu contraseña actual]
- **Uso**: Sistema principal a despertar

## 🔧 Optimizaciones Adicionales

### **En Manjaro Plasma:**
```bash
# Verificar que WOL persista después de suspensión
sudo systemctl status wol.service

# Si necesitas, reiniciar servicio WOL
sudo systemctl restart wol.service
```

### **En Manjaro XFCE:**
```bash
# Instalar herramientas adicionales
sudo pacman -S wakeonlan wol nmap

# Crear script de verificación rápida
echo '#!/bin/bash
ping -c 1 ************* && echo "Plasma está despierto" || echo "Plasma está dormido"' > ~/check_plasma.sh
chmod +x ~/check_plasma.sh
```

## 🌐 Ventajas de esta Solución

### **✅ Beneficios:**
- **🔒 Seguro**: Sin abrir puertos en router
- **🌍 Global**: Funciona desde cualquier lugar
- **💰 Gratis**: RustDesk es open source
- **⚡ Rápido**: 30-90 segundos para despertar
- **📱 Multiplataforma**: Apps para todos los dispositivos

### **📋 Requisitos:**
- Manjaro XFCE encendido la mayoría del tiempo
- Ambos sistemas en la misma red local
- RustDesk configurado en ambos

## 🧪 Pruebas Recomendadas

### **1. Prueba Local (en casa):**
```bash
# En XFCE, probar el script
./despertar_plasma_rustdesk.sh

# Verificar que Plasma despierte
ping *************
```

### **2. Prueba Remota:**
1. Salir de casa con móvil/laptop
2. Conectar a XFCE via RustDesk
3. Ejecutar script despertar
4. Conectar a Plasma via RustDesk

## 🚨 Solución de Problemas

### **Si Plasma no despierta:**
1. Verificar WOL: `./wake_my_computer.sh`
2. Verificar cable Ethernet conectado
3. Probar WOL manual: `wakeonlan fc:34:97:e1:9a:9b`

### **Si RustDesk no conecta:**
1. Verificar firewall: `sudo ufw status`
2. Reiniciar RustDesk en ambos sistemas
3. Verificar IDs y contraseñas

## 🎯 Próximos Pasos Inmediatos

1. **📝 Anotar tu ID de RustDesk Plasma** (ya lo tienes abierto)
2. **🔧 Configurar Manjaro XFCE** con RustDesk
3. **📋 Copiar script** `despertar_plasma_rustdesk.sh` a XFCE
4. **🧪 Probar localmente** el despertar
5. **📱 Configurar apps móviles** con ambos IDs
6. **🌐 Probar remotamente** desde fuera de casa

---

**¡Esta solución es mucho mejor que configurar el router! 🎉**
