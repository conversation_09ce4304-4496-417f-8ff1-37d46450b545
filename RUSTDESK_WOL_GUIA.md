# 🚀 Wake-On-LAN con RustDesk - Guía Completa

## 📋 Tu Configuración Actual
- **<PERSON><PERSON><PERSON> Principal**: <PERSON><PERSON><PERSON> (fc:34:97:e1:9a:9b)
- **Sistema Secundario**: Manjaro XFCE
- **Herramienta**: RustDesk instalado en ambos
- **Red Local**: ************/24

## 🎯 Soluciones Disponibles

### **Método 1: XFCE como Puente WOL**

#### Si Manjaro XFCE está siempre encendido:
1. **Conectarte a XFCE** via RustDesk desde cualquier lugar
2. **Ejecutar comando WOL** en XFCE para despertar Plasma
3. **Esperar 30 segundos** y conectarte a Plasma

#### Configuración en XFCE:
```bash
# Instalar wakeonlan en XFCE si no está
sudo pacman -S wakeonlan

# Crear script rápido
echo '#!/bin/bash
wakeonlan fc:34:97:e1:9a:9b
echo "Despertando Manjaro Plasma..."
echo "Espera 30 segundos y conéctate a RustDesk Plasma"' > ~/despertar_plasma.sh

chmod +x ~/despertar_plasma.sh
```

### **Método 2: Script Automático en RustDesk**

#### Crear acceso directo en XFCE:
1. **Escritorio** → Clic derecho → "Crear lanzador"
2. **Nombre**: "Despertar Plasma"
3. **Comando**: `/home/<USER>/despertar_plasma.sh`
4. **Icono**: Elegir uno reconocible

### **Método 3: RustDesk con Automatización**

#### Script avanzado para XFCE:
```bash
#!/bin/bash
# despertar_y_conectar.sh

echo "=== Despertando Manjaro Plasma ==="
wakeonlan fc:34:97:e1:9a:9b

echo "Esperando que Plasma inicie..."
for i in {30..1}; do
    echo "Esperando $i segundos..."
    sleep 1
done

echo "Plasma debería estar listo."
echo "Conéctate ahora a RustDesk Plasma"

# Opcional: Intentar ping para verificar
if ping -c 1 ************* > /dev/null 2>&1; then
    echo "✅ Plasma está respondiendo"
    # Opcional: Abrir RustDesk automáticamente
    # rustdesk &
else
    echo "⏳ Plasma aún no responde, espera un poco más"
fi
```

## 🔧 Configuración Paso a Paso

### **En Manjaro XFCE:**

1. **Instalar herramientas WOL**:
```bash
sudo pacman -S wakeonlan wol
```

2. **Crear script de despertar**:
```bash
cat > ~/despertar_plasma.sh << 'EOF'
#!/bin/bash
echo "🚀 Despertando Manjaro Plasma..."
wakeonlan fc:34:97:e1:9a:9b
echo "✅ Magic packet enviado"
echo "⏳ Espera 30-60 segundos"
echo "🖥️ Luego conéctate a RustDesk Plasma"
EOF

chmod +x ~/despertar_plasma.sh
```

3. **Probar el script**:
```bash
./despertar_plasma.sh
```

### **En Manjaro Plasma (cuando esté despierto):**

1. **Verificar RustDesk configurado**:
```bash
# Verificar que RustDesk esté corriendo
ps aux | grep rustdesk
```

2. **Configurar inicio automático**:
   - Sistema → Configuración → Inicio automático
   - Agregar RustDesk si no está

## 📱 Uso Remoto con RustDesk

### **Desde cualquier dispositivo:**

1. **Abrir RustDesk** en tu móvil/PC remoto
2. **Conectar a Manjaro XFCE** (siempre encendido)
3. **Ejecutar script** `./despertar_plasma.sh`
4. **Esperar 30-60 segundos**
5. **Conectar a Manjaro Plasma** via RustDesk

### **Apps RustDesk:**
- **Android**: RustDesk desde Play Store
- **iOS**: RustDesk desde App Store
- **Windows/Mac**: Cliente RustDesk

## 🔄 Automatización Avanzada

### **Script con Notificaciones:**
```bash
#!/bin/bash
# despertar_plasma_avanzado.sh

PLASMA_IP="*************"
PLASMA_MAC="fc:34:97:e1:9a:9b"

echo "🚀 Iniciando Wake-On-LAN para Plasma..."

# Verificar si ya está encendido
if ping -c 1 $PLASMA_IP > /dev/null 2>&1; then
    echo "✅ Plasma ya está encendido"
    exit 0
fi

# Enviar magic packet
echo "📡 Enviando magic packet..."
wakeonlan $PLASMA_MAC

# Esperar y verificar
echo "⏳ Esperando que Plasma inicie..."
for i in {60..1}; do
    if ping -c 1 $PLASMA_IP > /dev/null 2>&1; then
        echo "✅ Plasma está listo! ($((61-i)) segundos)"
        echo "🖥️ Puedes conectarte a RustDesk Plasma ahora"
        exit 0
    fi
    echo "Esperando... $i segundos restantes"
    sleep 1
done

echo "⚠️ Plasma no respondió en 60 segundos"
echo "Verifica que esté conectado por cable Ethernet"
```

## 🌐 Ventajas de esta Solución

### **✅ Beneficios:**
- **Sin configurar router**: No necesitas port forwarding
- **Funciona desde cualquier lugar**: Internet + RustDesk
- **Seguro**: Conexión encriptada de RustDesk
- **Fácil de usar**: Un clic para despertar
- **Gratuito**: RustDesk es open source

### **📋 Requisitos:**
- Manjaro XFCE siempre encendido (o la mayoría del tiempo)
- RustDesk configurado en ambos sistemas
- Ambos sistemas en la misma red local

## 🔧 Configuración Adicional

### **Optimizar RustDesk:**
1. **Configurar contraseña fija** en ambos sistemas
2. **Habilitar conexión automática** si es posible
3. **Configurar calidad** según tu conexión

### **Backup con TeamViewer:**
Si tienes TeamViewer también:
- Configurar Wake-On-LAN en TeamViewer
- Usar como alternativa a RustDesk

## 🚨 Solución de Problemas

### **Si XFCE no puede despertar Plasma:**
1. Verificar que ambos estén en la misma red
2. Probar WOL local: `wakeonlan fc:34:97:e1:9a:9b`
3. Verificar configuración WOL en Plasma

### **Si RustDesk no conecta:**
1. Verificar firewall en ambos sistemas
2. Reiniciar servicio RustDesk
3. Verificar configuración de red

## 🎯 Próximos Pasos

1. **Configurar script** en Manjaro XFCE
2. **Probar localmente** el Wake-On-LAN
3. **Probar remotamente** via RustDesk
4. **Crear accesos directos** para facilitar uso
5. **Documentar IDs** de RustDesk para acceso rápido
