# 🔧 Configuración Wake-On-LAN - Router Hitron Technologies

## 📋 Información de tu Router
- **Marca**: Hitron Technologies
- **IP**: ************
- **URL de acceso**: https://************/status_deviceinfo.html
- **Servidor web**: GoAhead-Webs

## 🔑 Credenciales para Probar

### Credenciales Comunes Hitron:
1. **Usuario**: `admin` | **Contraseña**: `admin`
2. **Usuario**: `admin` | **Contraseña**: `password`
3. **Usuario**: `cusadmin` | **Contraseña**: `password`
4. **Usuario**: `admin` | **Contraseña**: (tu contraseña WiFi)
5. **Usuario**: `admin` | **Contraseña**: (vacía)

### 📍 Dónde Encontrar las Credenciales:
1. **Etiqueta del router**: Busca en la parte inferior/trasera
2. **Factura del ISP**: A veces viene en la documentación
3. **Contraseña WiFi**: Muchos Hitron usan la misma contraseña

## 🌐 Pasos para Acceder

### 1. Abrir Navegador
- Ve a: **https://************/status_deviceinfo.html**
- Si aparece advertencia de seguridad, haz clic en "Avanzado" → "Continuar"

### 2. Probar Credenciales
Prueba en este orden:
1. admin / admin
2. admin / password
3. cusadmin / password

### 3. Si No Funciona
- Reinicia el router (desconectar 30 segundos)
- Prueba en modo incógnito del navegador
- Usa Firefox si estás en Chrome (o viceversa)

## 🔧 Configurar Port Forwarding en Hitron

### Una vez dentro del router:

1. **Buscar sección**:
   - "Port Forwarding"
   - "Virtual Servers"
   - "Firewall" → "Port Forwarding"
   - "Advanced" → "Port Forwarding"

2. **Agregar regla WOL**:
   ```
   Nombre/Description: WOL-Remote
   Protocolo: UDP
   Puerto Externo: 9
   IP Interna: **************
   Puerto Interno: 9
   Estado: Habilitado/Enabled
   ```

3. **Configuraciones adicionales** (si están disponibles):
   - **Directed Broadcast**: Habilitado
   - **WAN Access**: Habilitado
   - **Firewall**: Permitir puerto 9 UDP

## 🚨 Problemas Comunes con Hitron

### Router Bloqueado por ISP:
Algunos proveedores bloquean ciertas configuraciones:
- **Solución**: Contactar al ISP para habilitar port forwarding
- **Alternativa**: Usar modo bridge + router propio

### Interfaz Limitada:
Algunos Hitron tienen funciones limitadas:
- **Verificar**: Si tienes acceso completo o solo básico
- **Solución**: Pedir al ISP acceso completo

### Firmware Restrictivo:
- **Verificar versión**: En la página de estado
- **Actualizar**: Si hay versión más nueva disponible

## 🔄 Reset de Fábrica (Último Recurso)

### Si nada funciona:
1. **Localizar botón reset** (pequeño, en la parte trasera)
2. **Con router encendido**: Mantener presionado 10-15 segundos
3. **Esperar**: Router se reiniciará (2-3 minutos)
4. **Credenciales por defecto**: admin/admin o admin/password

⚠️ **ADVERTENCIA**: Reset borrará toda la configuración WiFi

## 📞 Contactar ISP

### Si el router es del proveedor:
- **Explicar**: Necesitas configurar port forwarding para Wake-On-LAN
- **Solicitar**: Acceso completo al router o configuración remota
- **Alternativa**: Modo bridge para usar tu propio router

### Información para el ISP:
- Necesitas abrir puerto UDP 9
- Destino: ************** (broadcast)
- Propósito: Wake-On-LAN remoto

## 🎯 Próximos Pasos

1. **Acceder al router** con las credenciales proporcionadas
2. **Buscar Port Forwarding** en el menú
3. **Configurar regla WOL** según las especificaciones
4. **Probar** con: `./test_wol_remote.sh`

## 📱 Una vez configurado

Podrás usar Wake-On-LAN remoto con:
- **IP**: *************
- **Puerto**: 9
- **MAC**: fc:34:97:e1:9a:9b

### Comando:
```bash
wakeonlan -i ************* -p 9 fc:34:97:e1:9a:9b
```
