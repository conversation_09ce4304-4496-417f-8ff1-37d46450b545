#!/bin/bash

# Script para verificar si el puerto WOL está abierto
PUBLIC_IP="*************"
WOL_PORT="9"

echo "=== Verificación Puerto WOL ==="
echo "IP Pública: $PUBLIC_IP"
echo "Puerto WOL: $WOL_PORT"
echo ""

echo "Verificando si el puerto $WOL_PORT está abierto..."

# Verificar con nmap si está instalado
if command -v nmap &> /dev/null; then
    echo "Usando nmap para verificar puerto..."
    nmap -sU -p $WOL_PORT $PUBLIC_IP
else
    echo "nmap no está instalado. Instalando..."
    sudo pacman -S nmap
    echo "Verificando puerto con nmap..."
    nmap -sU -p $WOL_PORT $PUBLIC_IP
fi

echo ""
echo "Si el puerto aparece como 'open' o 'open|filtered', está configurado correctamente."
echo "Si aparece como 'closed' o 'filtered', necesitas configurar port forwarding en tu router."

echo ""
echo "=== Configuración del Router Requerida ==="
echo "1. Accede a tu router: http://************"
echo "2. Busca 'Port Forwarding' o 'Virtual Servers'"
echo "3. Agrega regla:"
echo "   - Puerto Externo: $WOL_PORT (UDP)"
echo "   - IP Interna: **************"
echo "   - Puerto Interno: $WOL_PORT"
