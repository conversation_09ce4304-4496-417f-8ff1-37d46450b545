# 🔧 Acceso a BIOS/UEFI - ASUS

## 📋 Información de tu Sistema
- **Fabricante**: Compumax Computer S.A.S
- **Modelo**: 1046-900-0022
- **Placa Base**: ASUSTeK COMPUTER INC.
- **Tipo**: ASUS UEFI BIOS

## 🚀 Métodos para Acceder a BIOS

### **Método 1: Teclas de Acceso Rápido (Más Común)**

#### **Teclas ASUS más comunes:**
- **F2** (más común en ASUS)
- **DEL** (Delete)
- **F8**
- **F12** (Boot Menu)

#### **Procedimiento:**
1. **Reiniciar el equipo**
2. **Presionar repetidamente** la tecla desde que aparece el logo
3. **Mantener presionada** hasta que aparezca la BIOS

### **Método 2: Desde Windows/Linux (Acceso Avanzado)**

#### **<PERSON><PERSON> (tu sistema actual):**
```bash
# Reiniciar directamente a UEFI
sudo systemctl reboot --firmware-setup
```

### **Método 3: Configuración de Arranque**

#### **Deshabilitar Fast Boot primero:**
1. **Reiniciar** y presionar **F2** o **DEL**
2. Buscar **"Fast Boot"** y **deshabilitarlo**
3. **Guardar y salir**
4. Reiniciar y probar acceso normal

## 🎯 Configuración Wake-On-LAN en BIOS ASUS

### **Una vez en la BIOS, buscar:**

#### **Sección Advanced:**
- **Advanced** → **APM Configuration**
- **Advanced** → **Power Management**
- **Advanced** → **Onboard Devices Configuration**

#### **Opciones a HABILITAR:**
- ✅ **Wake On LAN**
- ✅ **Wake On PCI-E**
- ✅ **Power On By PCI-E**
- ✅ **Resume By LAN**
- ✅ **PME Event Wake Up**

#### **Opciones a DESHABILITAR:**
- ❌ **ErP Ready** (Energy Related Products)
- ❌ **Deep Sleep**
- ❌ **Fast Boot** (temporalmente para configurar)

### **Ubicaciones Típicas en ASUS:**

#### **Opción 1: Advanced → APM Configuration**
```
Advanced
├── APM Configuration
    ├── Power On By PCI-E [Enabled]
    ├── Power On By Ring [Enabled]
    └── Wake On LAN [Enabled]
```

#### **Opción 2: Advanced → Power Management**
```
Advanced
├── Power Management Setup
    ├── Resume By LAN [Enabled]
    ├── PME Event Wake Up [Enabled]
    └── ErP Ready [Disabled]
```

#### **Opción 3: Advanced → Onboard Devices**
```
Advanced
├── Onboard Devices Configuration
    ├── Onboard LAN Controller [Enabled]
    └── Onboard LAN Boot ROM [Enabled]
```

## 🔧 Procedimiento Paso a Paso

### **1. Acceder a BIOS:**
```bash
# Método fácil desde Linux:
sudo systemctl reboot --firmware-setup
```

### **2. Navegar en BIOS ASUS:**
- **Flechas**: Navegar menús
- **Enter**: Seleccionar/Cambiar
- **F10**: Guardar y salir
- **ESC**: Volver atrás

### **3. Configurar Wake-On-LAN:**
1. **Advanced** → **APM Configuration**
2. **Power On By PCI-E** → **Enabled**
3. **Wake On LAN** → **Enabled**
4. **F10** → **Save & Exit**

### **4. Verificar después del reinicio:**
```bash
# Verificar que WOL sigue activo
sudo ethtool eno1 | grep Wake-on
```

## 🚨 Solución de Problemas

### **Si no puedes acceder:**

#### **Problema: Fast Boot habilitado**
- **Solución**: Mantener **Shift** mientras reinicias
- **O**: Usar `sudo systemctl reboot --firmware-setup`

#### **Problema: Secure Boot**
- **Solución**: Deshabilitar **Secure Boot** temporalmente
- **Ubicación**: **Boot** → **Secure Boot** → **Disabled**

#### **Problema: UEFI vs Legacy**
- **Verificar**: **Boot** → **Boot Mode**
- **Recomendado**: **UEFI Mode**

### **Si WOL no funciona después:**

#### **Verificar configuración:**
```bash
# Estado actual
sudo ethtool eno1 | grep Wake-on

# Reconfigurar si es necesario
sudo ethtool -s eno1 wol g
sudo systemctl restart wol.service
```

## 📱 Acceso Alternativo

### **Si tienes problemas con teclas:**

#### **Método Boot Menu:**
1. **Reiniciar** y presionar **F8** o **F12**
2. Seleccionar **"Enter Setup"** o **"BIOS Setup"**

#### **Método desde Grub:**
1. **Reiniciar** y en Grub presionar **'c'**
2. Escribir: `fwsetup`
3. Presionar **Enter**

## 🎯 Configuración Recomendada Final

### **Configuraciones BIOS para WOL óptimo:**
```
Advanced → APM Configuration:
├── Power On By PCI-E: [Enabled]
├── Power On By Ring: [Enabled]
├── Wake On LAN: [Enabled]
└── ErP Ready: [Disabled]

Advanced → Power Management:
├── Resume By LAN: [Enabled]
├── PME Event Wake Up: [Enabled]
└── Deep Sleep: [Disabled]

Boot:
├── Fast Boot: [Disabled] (opcional)
└── Secure Boot: [Enabled] (mantener si es posible)
```

## ✅ Verificación Post-Configuración

### **Después de configurar BIOS:**
```bash
# 1. Verificar WOL
./wake_my_computer.sh

# 2. Probar WOL local
./test_wol.sh

# 3. Probar con RustDesk
./despertar_plasma_rustdesk.sh (desde XFCE)
```

---

**¡Con BIOS ASUS configurada correctamente, Wake-On-LAN funcionará perfectamente!**
