# 🌐 Wake-On-LAN Remoto - Guía Completa

## 📋 Tu Información de Red
- **IP Pública**: *************
- **IP Local**: ************3
- **MAC Address**: fc:34:97:e1:9a:9b
- **Gateway/Router**: ************

## 🔧 Configuración del Router (OBLIGATORIO)

Para usar WOL desde internet, necesitas configurar tu router:

### 1. Acceder al Router
- Abre un navegador y ve a: `http://************`
- Usuario/contraseña: admin/admin (o revisa la etiqueta del router)

### 2. Configurar Port Forwarding
Busca la sección "Port Forwarding" o "Virtual Servers" y agrega:

```
Nombre: WOL-Remote
Protocolo: UDP
Puerto Externo: 9 (o 7, 9999)
IP Interna: ************** (broadcast)
Puerto Interno: 9
Estado: Habilitado
```

### 3. Habilitar Directed Broadcast (si está disponible)
- Bus<PERSON> "Directed Broadcast" o "Broadcast Forwarding"
- Habilitarlo para permitir magic packets desde internet

## 🚀 Métodos para WOL Remoto

### Método 1: Comando Directo
```bash
# Desde cualquier lugar del mundo:
wakeonlan -i ************* -p 9 fc:34:97:e1:9a:9b
```

### Método 2: Script Personalizado
```bash
#!/bin/bash
# Despertar equipo remotamente
wakeonlan -i ************* -p 9 fc:34:97:e1:9a:9b
echo "Magic packet enviado a *************"
```

## 📱 Apps Móviles para WOL Remoto

### Android:
- **Wake On Lan** (Mike Webb)
  - IP: *************
  - Puerto: 9
  - MAC: fc:34:97:e1:9a:9b

### iOS:
- **Mocha WOL**
  - Host: *************
  - Puerto: 9
  - MAC: fc:34:97:e1:9a:9b

## 🔒 Alternativa Segura: VPN

### Opción 1: WireGuard (Recomendado)
1. Configurar servidor WireGuard en tu router o equipo
2. Conectarte por VPN desde cualquier lugar
3. Usar WOL local: `wol fc:34:97:e1:9a:9b`

### Opción 2: OpenVPN
Similar a WireGuard pero más complejo de configurar

## 🌍 IP Dinámica - Solución DynDNS

Si tu IP pública cambia, usa servicios gratuitos:

### No-IP (Gratuito)
1. Registrarse en: https://www.noip.com
2. Crear hostname: `tu-casa.ddns.net`
3. Configurar en router o instalar cliente

### DuckDNS (Gratuito)
1. Ir a: https://www.duckdns.org
2. Crear dominio: `tu-casa.duckdns.org`
3. Configurar actualización automática

## 🧪 Scripts de Prueba

### Verificar Puerto Abierto
```bash
./check_wol_port.sh
```

### Despertar Remotamente
```bash
./wake_remote.sh
```

### Configurar DynDNS
```bash
./setup_dyndns.sh
```

## ⚠️ Consideraciones de Seguridad

### Riesgos:
- Abrir puertos puede exponer tu red
- Magic packets no están encriptados

### Recomendaciones:
1. **Usar VPN** (más seguro que port forwarding)
2. **Cambiar puerto por defecto** (usar 9999 en lugar de 9)
3. **Configurar firewall** en el router
4. **Monitorear logs** del router

## 🔧 Configuración Paso a Paso del Router

### 1. Acceder al Router
```
URL: http://************
Usuario: admin (o revisa etiqueta del router)
Contraseña: admin (o revisa etiqueta del router)
```

### 2. Configurar Port Forwarding
Busca una de estas secciones:
- "Port Forwarding"
- "Virtual Servers"
- "NAT Forwarding"
- "Applications & Gaming"

### 3. Agregar Regla WOL
```
Nombre/Descripción: WOL-Remote
Protocolo: UDP
Puerto Externo: 9 (o 9999 para más seguridad)
IP Interna: ************** (dirección broadcast)
Puerto Interno: 9 (mismo que externo)
Estado: Habilitado/Enabled
```

### 4. Configuraciones Adicionales (si están disponibles)
- **Directed Broadcast**: Habilitado
- **WAN Ping**: Habilitado (opcional, para ping tests)
- **UPnP**: Habilitado (puede ayudar)

## 🌐 Uso desde Internet

### Comando desde Linux/macOS:
```bash
wakeonlan -i ************* -p 9 fc:34:97:e1:9a:9b
```

### Comando desde Windows:
```cmd
wakeonlan.exe -i ************* -p 9 fc:34:97:e1:9a:9b
```

### Apps Móviles - Configuración:
- **Host/IP**: *************
- **Puerto**: 9
- **MAC**: fc:34:97:e1:9a:9b
- **Protocolo**: UDP

## 🚨 Solución de Problemas

### WOL Remoto No Funciona:

1. **Verificar configuración local**:
   ```bash
   ./wake_my_computer.sh
   ```

2. **Verificar puerto abierto**:
   ```bash
   ./check_wol_port.sh
   ```

3. **Probar desde red local primero**:
   ```bash
   wol fc:34:97:e1:9a:9b
   ```

4. **Verificar logs del router**:
   - Buscar sección "Logs" o "System Log"
   - Verificar si llegan paquetes al puerto 9

### Errores Comunes:

- **"Connection refused"**: Puerto no configurado en router
- **"Network unreachable"**: IP pública incorrecta
- **"Permission denied"**: Ejecutar con sudo si es necesario

## 📞 Alternativas Avanzadas

### 1. SSH Tunnel + WOL
```bash
# Desde dispositivo remoto:
ssh -L 9:**************:9 usuario@*************
wakeonlan -i localhost -p 9 fc:34:97:e1:9a:9b
```

### 2. Telegram Bot para WOL
- Crear bot de Telegram
- Script que responda a comandos
- Enviar magic packet cuando reciba "/wake"

### 3. Página Web Simple
- Crear página web con botón
- Script PHP/Python que ejecute wakeonlan
- Acceso desde cualquier navegador

## 📝 Resumen de Archivos Creados

- `WOL_REMOTO_GUIA.md` - Esta guía completa
- `wake_remote.sh` - Script para despertar remotamente
- `check_wol_port.sh` - Verificar configuración del router
- `setup_dyndns.sh` - Configurar dominio dinámico
- `update_dyndns.sh` - Actualizar IP dinámica (se crea automáticamente)

## ✅ Lista de Verificación

- [ ] WOL configurado localmente
- [ ] Router configurado (port forwarding)
- [ ] Puerto verificado como abierto
- [ ] Prueba local exitosa
- [ ] Prueba remota exitosa
- [ ] DynDNS configurado (opcional)
- [ ] Apps móviles configuradas
