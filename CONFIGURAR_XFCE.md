# 🖥️ Configuración Manjaro XFCE para Wake-On-LAN Remoto

## 📋 Información del Sistema Plasma (ya configurado)
- **ID RustDesk**: 400661147
- **Contraseña**: WakeUp2025!
- **MAC**: fc:34:97:e1:9a:9b
- **IP**: *************

## 🔧 Pasos en Manjaro XFCE

### **1. Instalar RustDesk en XFCE**
```bash
# Instalar RustDesk via Flatpak
flatpak install flathub com.rustdesk.RustDesk

# O si prefieres versión nativa
yay -S rustdesk-bin
```

### **2. Instalar Herramientas Wake-On-LAN**
```bash
# Instalar wakeonlan
sudo pacman -S wakeonlan wol

# Verificar instalación
which wakeonlan
```

### **3. Transferir Script de Despertar**
```bash
# Opción A: USB/Disco externo
# Copiar despertar_plasma_rustdesk.sh desde Plasma a USB
# Luego en XFCE:
cp /media/usb/despertar_plasma_rustdesk.sh ~/
chmod +x ~/despertar_plasma_rustdesk.sh

# Opción B: Red local (si SSH está habilitado)
scp usuario@*************:~/Documentos/Wake-On-Lan/despertar_plasma_rustdesk.sh ~/

# Opción C: Carpeta compartida en red
# Configurar carpeta compartida entre ambos sistemas
```

### **4. Configurar RustDesk en XFCE**
```bash
# Ejecutar RustDesk
flatpak run com.rustdesk.RustDesk &

# En la interfaz de RustDesk:
# 1. Anotar el ID que aparece
# 2. Configurar contraseña: WakeUp2025!
# 3. Habilitar "Enable Password"
# 4. Habilitar "Enable Auto Login" (opcional)
```

### **5. Probar Wake-On-LAN Local**
```bash
# Desde XFCE, probar despertar Plasma
wakeonlan fc:34:97:e1:9a:9b

# Verificar que Plasma despierta
ping *************

# Probar script completo
./despertar_plasma_rustdesk.sh
```

### **6. Crear Acceso Directo en Escritorio**
```bash
# Crear lanzador en escritorio XFCE
cat > ~/Desktop/Despertar-Plasma.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Despertar Manjaro Plasma
Comment=Despertar Plasma via Wake-On-LAN
Exec=/home/<USER>/despertar_plasma_rustdesk.sh
Icon=computer
Terminal=true
Categories=System;
EOF

# Hacer ejecutable
chmod +x ~/Desktop/Despertar-Plasma.desktop
```

## 📱 Configuración Apps Móviles

### **Una vez configurado XFCE, en tus apps móviles:**

#### **Conexión 1: Manjaro XFCE (Puente)**
- **Nombre**: "XFCE Bridge"
- **ID**: [El ID que aparezca en XFCE]
- **Contraseña**: WakeUp2025!
- **Uso**: Para despertar Plasma

#### **Conexión 2: Manjaro Plasma (Principal)**
- **Nombre**: "Plasma Main"
- **ID**: 400661147
- **Contraseña**: WakeUp2025!
- **Uso**: Sistema principal

## 🚀 Flujo de Uso Remoto Completo

### **Desde cualquier lugar del mundo:**
1. **📱 Abrir RustDesk** en móvil/PC remoto
2. **🔗 Conectar a "XFCE Bridge"**
3. **⚡ Doble clic en "Despertar-Plasma"** (escritorio)
   - O ejecutar: `./despertar_plasma_rustdesk.sh`
4. **⏳ Esperar 30-90 segundos**
5. **🖥️ Conectar a "Plasma Main"** (ID: 400661147)
6. **🎉 ¡Usar tu sistema remotamente!**

## 🧪 Pruebas Recomendadas

### **Prueba 1: Local (en casa)**
```bash
# En XFCE, probar despertar
./despertar_plasma_rustdesk.sh
```

### **Prueba 2: Remota (fuera de casa)**
1. Salir de casa con móvil
2. Conectar a XFCE via RustDesk
3. Ejecutar script despertar
4. Conectar a Plasma via RustDesk

## 📝 Notas Importantes

- **XFCE debe estar siempre encendido** (o la mayoría del tiempo)
- **Ambos sistemas deben estar en la misma red local**
- **Usar cable Ethernet** para Wake-On-LAN (no WiFi)
- **Contraseña única**: WakeUp2025! para ambos sistemas

## 🔧 Comandos Útiles para XFCE

```bash
# Verificar si Plasma está despierto
ping *************

# Despertar Plasma manualmente
wakeonlan fc:34:97:e1:9a:9b

# Ver procesos RustDesk
ps aux | grep rustdesk

# Ejecutar RustDesk
flatpak run com.rustdesk.RustDesk &
```

---

**¡Con esta configuración tendrás Wake-On-LAN remoto sin configurar el router!** 🎉
