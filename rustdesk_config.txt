# Configuración RustDesk + Wake-On-LAN
# Fecha: vie 18 jul 2025 21:00:56 -05

## <PERSON><PERSON><PERSON> (sistema principal)
ID_PLASMA=[Anotar aquí tu ID de RustDesk]
PASSWORD_PLASMA=[Tu contraseña configurada]
IP_PLASMA=*************
MAC_PLASMA=fc:34:97:e1:9a:9b

## Manjaro XFCE (sistema puente)
ID_XFCE=[Configurar después]
PASSWORD_XFCE=[Configurar después]

## Comandos útiles
# Despertar Plasma desde XFCE:
# ./despertar_plasma_rustdesk.sh

# Verificar WOL local:
# ./wake_my_computer.sh

# Verificar si Plasma está despierto:
# ping *************
