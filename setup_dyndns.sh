#!/bin/bash

# Script para configurar DynDNS con DuckDNS (gratuito)

echo "=== Configuración DynDNS con DuckDNS ==="
echo ""
echo "DuckDNS es un servicio gratuito para IP dinámicas"
echo ""
echo "Pasos para configurar:"
echo "1. Ve a: https://www.duckdns.org"
echo "2. Inicia sesión con Google/GitHub/Reddit"
echo "3. Crea un subdominio, ejemplo: 'mi-casa'"
echo "4. Obtendrás: mi-casa.duckdns.org"
echo "5. Copia tu token de DuckDNS"
echo ""

read -p "¿Has creado tu cuenta en DuckDNS? (s/n): " created
if [ "$created" != "s" ]; then
    echo "Primero crea tu cuenta en https://www.duckdns.org"
    exit 1
fi

read -p "Ingresa tu subdominio (sin .duckdns.org): " subdomain
read -p "Ingresa tu token de DuckDNS: " token

if [ -z "$subdomain" ] || [ -z "$token" ]; then
    echo "Error: Necesitas proporcionar subdominio y token"
    exit 1
fi

# Crear script de actualización
cat > update_dyndns.sh << EOF
#!/bin/bash
# Script de actualización DynDNS
SUBDOMAIN="$subdomain"
TOKEN="$token"

# Obtener IP actual
CURRENT_IP=\$(curl -s ifconfig.me)

# Actualizar DuckDNS
RESPONSE=\$(curl -s "https://www.duckdns.org/update?domains=\$SUBDOMAIN&token=\$TOKEN&ip=\$CURRENT_IP")

if [ "\$RESPONSE" = "OK" ]; then
    echo "\$(date): IP actualizada a \$CURRENT_IP para \$SUBDOMAIN.duckdns.org"
else
    echo "\$(date): Error actualizando IP: \$RESPONSE"
fi
EOF

chmod +x update_dyndns.sh

echo ""
echo "✅ Script de actualización creado: update_dyndns.sh"
echo ""
echo "Tu dominio será: $subdomain.duckdns.org"
echo ""
echo "Para automatizar la actualización, agrega a crontab:"
echo "*/5 * * * * /home/<USER>/Documentos/Wake-On-Lan/update_dyndns.sh >> /var/log/dyndns.log"
echo ""
echo "Ahora podrás usar WOL con:"
echo "wakeonlan -i $subdomain.duckdns.org -p 9 fc:34:97:e1:9a:9b"
