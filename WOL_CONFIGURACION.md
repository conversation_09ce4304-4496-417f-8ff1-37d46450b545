# Configuración Wake-On-LAN - Manjaro Plasma

## ✅ Estado de la Configuración
Wake-On-LAN está **CONFIGURADO Y ACTIVO** en tu sistema.

## 📋 Información del Sistema
- **Interfaz de red**: eno1
- **Dirección MAC**: fc:34:97:e1:9a:9b
- **IP actual**: ************3
- **Red**: ************/24
- **Gateway**: ************

## 🔧 Configuración Realizada

### 1. Software Instalado
- `ethtool` - Para configurar WOL
- `wol` - Para enviar magic packets

### 2. Servicio Systemd Creado
- Archivo: `/etc/systemd/system/wol.service`
- Estado: Habilitado y activo
- Función: Configura WOL automáticamente al iniciar el sistema

### 3. Configuración Actual
```bash
Supports Wake-on: pumbg
Wake-on: g
```
- **g** = Magic Packet (configuración correcta)

## 🚀 Cómo Usar Wake-On-LAN

### Desde Linux/macOS:
```bash
wol fc:34:97:e1:9a:9b
```

### Desde Windows:
1. Instalar `wakeonlan` o usar PowerShell
2. Ejecutar: `wakeonlan fc:34:97:e1:9a:9b`

### Desde Aplicaciones Móviles:
- Buscar "Wake on LAN" en tu tienda de apps
- Configurar con MAC: `fc:34:97:e1:9a:9b`

## ⚠️ Configuración BIOS/UEFI Requerida

**IMPORTANTE**: Para que WOL funcione después de apagones, necesitas verificar en la BIOS/UEFI:

1. Reinicia y entra a la BIOS/UEFI (generalmente F2, F12, o DEL al iniciar)
2. Busca estas opciones y habilítalas:
   - **Wake on LAN** o **WOL**
   - **Power on by PCI-E** o **Power on by Ethernet**
   - **Resume by LAN**
   - **Deep Sleep** (deshabilitarlo si existe)
   - **ErP Ready** (deshabilitarlo si existe)

## 🧪 Pruebas

### Prueba Local (solo para verificar que el comando funciona):
```bash
./test_wol.sh
```

### Prueba Real:
1. Apaga completamente el equipo
2. Desde otro dispositivo en la misma red, ejecuta:
   ```bash
   wol fc:34:97:e1:9a:9b
   ```
3. El equipo debería encenderse automáticamente

## 📱 Apps Móviles Recomendadas
- **Android**: "Wake On Lan" por Mike Webb
- **iOS**: "Mocha WOL" o "Wake On Lan"

## 🔍 Verificación del Estado
Para verificar que WOL sigue activo:
```bash
./wake_my_computer.sh
```

## 🛠️ Solución de Problemas

### Si WOL no funciona:
1. Verificar configuración BIOS/UEFI
2. Verificar que el equipo esté conectado por cable Ethernet
3. Verificar que el switch/router soporte WOL
4. Comprobar estado: `sudo ethtool eno1 | grep Wake-on`

### Reconfigurar WOL:
```bash
sudo systemctl restart wol.service
```

## 📝 Notas Importantes
- WOL solo funciona con conexión Ethernet (no WiFi)
- El equipo debe estar conectado a la corriente
- Algunos switches pueden filtrar magic packets
- La configuración persiste después de reinicios gracias al servicio systemd
